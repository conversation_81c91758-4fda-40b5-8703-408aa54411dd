'use client'

import { useCallback, useState } from 'react'
import { useTranslations } from 'next-intl'
import { Coupon, hasDecimal, mergeStyles, NCoinView, Price, RenderHtml } from '@ninebot/core'
import { Button } from 'antd-mobile'
import Big from 'big.js'

import { Sign, Tips } from '../icons'

import CustomPopup from './CustomPopup'

export interface CouponItemProps {
  coupon: Coupon
}

const CouponItem = ({ coupon }: CouponItemProps) => {
  const { expiration_date, rule_info, status } = coupon
  const [visible, setVisible] = useState(false)
  const getI18nString = useTranslations('Common')
  const disabled = status === 1

  /**
   * 渲染优惠信息
   */
  const renderDiscount = useCallback(() => {
    if (Number(rule_info.discount_amount) === 0) {
      return (
        <span
          className={mergeStyles(
            'text-[30px] leading-[32px] text-primary',
            disabled && 'text-[#6E6E73]',
          )}>
          {rule_info.label}
        </span>
      )
    }

    if (['cart_fixed', 'by_fixed'].includes(rule_info.simple_action)) {
      // 检查是否为 NCoin 类型
      const isNCoin = 'is_ncoin' in rule_info && rule_info.is_ncoin

      if (isNCoin) {
        return (
          <NCoinView
            number={rule_info.discount_amount}
            iconStyle={{ background: disabled ? '#6E6E73' : '#DA291C' }}
            textStyle={mergeStyles(
              'text-[22px] leading-[1.2] text-primary',
              disabled && 'text-[#6E6E73]',
            )}
          />
        )
      }

      return (
        <Price
          bold
          color="primary"
          currencyStyle={mergeStyles(
            'text-[18px] leading-[22px] font-miSansMedium380',
            disabled && 'text-[#6E6E73]',
          )}
          textStyle={mergeStyles('text-[40px] leading-none', disabled && 'text-[#6E6E73]')}
          showFraction={false}
          price={{ value: rule_info.discount_amount }}
        />
      )
    }

    if (['by_percent'].includes(rule_info.simple_action)) {
      const amount = new Big(100).minus(new Big(rule_info.discount_amount)).div(100).times(10)

      return (
        <div className="flex items-baseline">
          <span
            className={mergeStyles(
              'text-[40px] leading-none text-primary',
              disabled && 'text-[#6E6E73]',
            )}>
            {hasDecimal(amount.toNumber()) ? amount.toFixed(1) : amount.toString()}
          </span>
          <span
            className={mergeStyles(
              'text-[16px] leading-[19px] text-primary',
              disabled && 'text-[#6E6E73]',
            )}>
            {getI18nString('coupon_code_percent')}
          </span>
        </div>
      )
    }

    return (
      <span
        className={mergeStyles(
          'text-[40px] leading-none text-primary',
          disabled && 'text-[#6E6E73]',
        )}>
        {rule_info.discount_amount}
      </span>
    )
  }, [rule_info, disabled, getI18nString])

  return (
    <div
      className={mergeStyles(
        'relative flex items-center justify-between overflow-hidden rounded-[12px] p-[4px]',
        status === 0 ? 'bg-[#FEE5E5]' : 'bg-gray-base',
      )}>
      <div
        className={mergeStyles(
          'flex h-[61px] w-[96px] flex-col items-center justify-center',
          status === 0 ? 'text-primary' : 'text-[#6E6E73]',
        )}>
        {/* 优惠券金额 */}
        <div className="mb-[4px] flex items-baseline">{renderDiscount()}</div>
        {/* 使用条件 */}
        {rule_info.short_description && (
          <div className="text-center font-miSansRegular330 text-base leading-[1.2]">
            {rule_info.short_description}
          </div>
        )}
      </div>

      {/* 优惠券容器 - 添加relative定位以便定位半圆 */}
      <div className="relative h-[102px] w-[234px] rounded-[12px] bg-white">
        {/* 上侧半圆遮盖 */}
        <div
          className={mergeStyles(
            'absolute -top-[6px] left-2/3 h-[12px] w-[12px] rounded-full',
            status === 0 ? 'bg-[#FEE5E5]' : 'bg-gray-base',
          )}></div>

        {/* 下侧半圆遮盖 */}
        <div
          className={mergeStyles(
            'absolute -bottom-[6px] left-2/3 h-[12px] w-[12px] rounded-full',
            status === 0 ? 'bg-[#FEE5E5]' : 'bg-gray-base',
          )}></div>

        {/* 优惠券图标 */}
        <div className="absolute bottom-0 right-base-12">
          <Sign fill={status === 0 ? '#FEE5E5' : '#F3F3F4'} />
        </div>

        <div className="flex h-full">
          {/* 优惠券标题 */}
          <div className="flex flex-col justify-between px-[14px] py-[16px]">
            <div className="flex flex-col gap-[4px]">
              <div className="line-clamp-1 text-[16px] leading-[1.4] text-black">
                {rule_info.name}
              </div>
              {expiration_date ? (
                <div className="line-clamp-1 text-[12px] leading-[1.2] text-[#86868B]">
                  {expiration_date} {getI18nString('expires')}
                </div>
              ) : null}
            </div>
            {rule_info.description && (
              <button
                className="flex items-center"
                onClick={(e) => {
                  e.stopPropagation()
                  setVisible(true)
                }}>
                <span className="line-clamp-1 font-miSansRegular330 text-[12px] leading-[1.2] text-[#86868B]">
                  {getI18nString('my_coupon_detail')}
                </span>
                <span>
                  <Tips />
                </span>
              </button>
            )}
          </div>

          {/* 去使用按钮 目前不做 */}
          {/* <div className="flex items-center justify-center mr-[12px]">
            <Button
              className="coupon-btn"
              size="small"
              disabled={status === 1}
              onClick={onUse}
              style={{
                width: 68,
                height: 30,
                borderRadius: 101,
              }}>
              <span className='text-[12px] leading-[14.4px] font-miSansMedium380 text-[#DA291C]'>{getI18nString('my_coupon_use')}</span>
            </Button>
          </div> */}
        </div>

        {/* 详细信息 */}
        <CustomPopup
          visible={visible}
          onClose={() => setVisible(false)}
          showHeader={true}
          headTitle={getI18nString('my_coupon_detail_desc')}
          position="bottom">
          <div className="flex items-center gap-8 px-8 py-[24px]">
            <span className="text-justify text-[14px] leading-[22px] text-[#444446]">
              <RenderHtml content={rule_info.description} />
            </span>
          </div>
          <div className="absolute bottom-0 left-0 right-0 flex items-center justify-center px-base-24 py-base-16">
            <Button
              color="primary"
              className="nb-button !h-[40px] w-full"
              onClick={() => setVisible(false)}>
              <span className="font-miSansDemiBold450 text-[14px] leading-none text-[#FFFFFF]">
                {getI18nString('Ok')}
              </span>
            </Button>
          </div>
        </CustomPopup>
      </div>
    </div>
  )
}

export default CouponItem
